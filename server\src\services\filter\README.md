# 智能股票筛选服务

## 概述

智能股票筛选服务 (`intelligentStockFilterService.js`) 是一个基于AI的股票筛选工具，它可以将用户的自然语言描述转换为MongoDB查询脚本，并执行筛选返回符合条件的股票数据。

## 功能特性

- **自然语言解析**：使用DeepSeek AI将用户的中文描述转换为精确的数据库查询
- **多表关联查询**：支持当日数据、财务数据和基本信息的联合筛选
- **智能字段映射**：自动将英文字段名转换为中文显示
- **灵活的查询类型**：支持单表查询、多表查询和聚合查询
- **结果格式化**：返回格式化的中文结果，便于前端展示

## 使用方法

### 基本用法

```javascript
const { intelligentStockFilter } = require('./intelligentStockFilterService');

// 调用智能筛选
const result = await intelligentStockFilter(
  '找出市盈率在10-30倍之间，总市值超过100亿的股票',
  '2024-12-20'
);

console.log(result);
```

### 参数说明

- `userDescription` (string): 用户的自然语言描述
- `targetDate` (string): 目标日期，格式为 YYYY-MM-DD

### 返回结果

```javascript
{
  "success": true,
  "userDescription": "用户的原始描述",
  "targetDate": "2024-12-20",
  "queryScript": {
    // AI生成的查询脚本
    "queryType": "single",
    "description": "查询描述",
    "queries": [...],
    "resultFields": [...]
  },
  "totalCount": 25,
  "stocks": [
    {
      "证券代码": "000001",
      "证券简称": "平安银行",
      "市盈率TTM": 15.2,
      "总市值": "2500亿",
      // ... 其他字段
    }
    // ... 更多股票
  ],
  "timestamp": "2024-12-20T10:30:00.000Z"
}
```

## 支持的查询类型

### 1. 基本面指标筛选

```javascript
// 市盈率筛选
"找出市盈率在10-20倍之间的股票"

// 市值筛选
"筛选总市值超过500亿的大盘股"

// 市净率筛选
"找出市净率低于2倍的价值股"
```

### 2. 技术指标筛选

```javascript
// 涨跌幅筛选
"筛选今日涨幅超过5%的股票"

// 换手率筛选
"找出换手率在3%-10%之间的活跃股票"

// 振幅筛选
"筛选振幅超过8%的高波动股票"
```

### 3. 财务指标筛选

```javascript
// 盈利能力
"找出净资产收益率超过15%的高盈利股票"

// 成长性
"筛选净利润增长率超过20%的成长股"

// 偿债能力
"找出资产负债率低于50%的稳健股票"
```

### 4. 行业和板块筛选

```javascript
// 行业筛选
"找出银行行业的所有股票"

// 板块筛选
"筛选科创板的股票"

// 组合筛选
"找出创业板中市值超过100亿的科技股"
```

### 5. 复合条件筛选

```javascript
// 多条件组合
"找出银行行业中，市盈率低于10倍，净资产收益率超过12%的股票"

// 排序要求
"筛选市值超过1000亿的股票，按涨跌幅降序排列"

// 数量限制
"找出涨幅最大的前10只股票"
```

## 数据源说明

### 当日股票数据 (DailyStockData)
- 价格信息：收盘价、涨跌幅、最高价、最低价等
- 交易信息：成交量、成交额、换手率等
- 估值指标：市盈率、市净率、市值等
- 技术指标：振幅、涨速、52周高低点等

### 财务数据 (FinancialData)
- 盈利能力：净利润、净利润增长率、净资产收益率等
- 偿债能力：流动比率、资产负债率等
- 营运能力：存货周转率、应收账款周转天数等
- 成长性：营收增长率、利润增长率等

### 基本信息 (StockBasicInfo)
- 证券代码、证券简称
- 所属板块、行业分类
- 上市时间等

## 错误处理

服务包含完善的错误处理机制：

```javascript
try {
  const result = await intelligentStockFilter(userDescription, targetDate);
  // 处理成功结果
} catch (error) {
  console.error('筛选失败:', error.message);
  // 处理错误情况
}
```

常见错误类型：
- AI解析失败：用户描述不清楚或与股票无关
- 查询执行失败：生成的查询脚本有误
- 数据获取失败：数据库连接或查询问题

## 性能优化

- 使用索引优化查询性能
- 支持结果数量限制
- 智能缓存常用查询结果
- 异步并行处理多表查询

## 扩展性

服务设计具有良好的扩展性：
- 可以轻松添加新的数据表支持
- 支持自定义查询逻辑
- 可以集成更多AI模型
- 支持自定义结果格式化

## 注意事项

1. **API密钥配置**：确保正确配置DeepSeek API密钥
2. **数据库连接**：确保MongoDB连接正常
3. **日期格式**：目标日期必须是YYYY-MM-DD格式
4. **查询复杂度**：过于复杂的查询可能影响性能
5. **结果数量**：大量结果可能需要分页处理
