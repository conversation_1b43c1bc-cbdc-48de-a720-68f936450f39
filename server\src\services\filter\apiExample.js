/**
 * 智能股票筛选API使用示例
 * 
 * 这个文件展示了如何在Express.js应用中集成智能股票筛选服务
 * 注意：这只是一个示例，实际使用时需要根据项目结构调整
 */

const express = require('express');
const { intelligentStockFilter } = require('./intelligentStockFilterService');

// 创建路由器（在实际项目中，这应该放在routes文件夹中）
const router = express.Router();

/**
 * POST /api/filter/intelligent
 * 智能股票筛选接口
 * 
 * 请求体：
 * {
 *   "description": "用户的自然语言描述",
 *   "targetDate": "2024-12-20"
 * }
 * 
 * 响应：
 * {
 *   "code": 200,
 *   "message": "筛选成功",
 *   "data": {
 *     "success": true,
 *     "userDescription": "...",
 *     "targetDate": "...",
 *     "queryScript": {...},
 *     "totalCount": 25,
 *     "stocks": [...],
 *     "timestamp": "..."
 *   }
 * }
 */
router.post('/intelligent', async (req, res) => {
  try {
    const { description, targetDate } = req.body;

    // 参数验证
    if (!description || typeof description !== 'string') {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的筛选描述',
        data: null
      });
    }

    if (!targetDate || !/^\d{4}-\d{2}-\d{2}$/.test(targetDate)) {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的目标日期（格式：YYYY-MM-DD）',
        data: null
      });
    }

    // 调用智能筛选服务
    const result = await intelligentStockFilter(description, targetDate);

    // 返回成功结果
    res.json({
      code: 200,
      message: '筛选成功',
      data: result
    });

  } catch (error) {
    console.error('智能筛选API错误:', error);
    
    // 返回错误信息
    res.status(500).json({
      code: 500,
      message: error.message || '筛选失败，请稍后重试',
      data: null
    });
  }
});

/**
 * GET /api/filter/examples
 * 获取筛选示例
 */
router.get('/examples', (req, res) => {
  const examples = [
    {
      category: '基本面筛选',
      examples: [
        '找出市盈率在10-30倍之间，总市值超过100亿的股票',
        '筛选市净率低于2倍的价值股',
        '找出股息率超过3%的高分红股票'
      ]
    },
    {
      category: '技术指标筛选',
      examples: [
        '筛选今日涨幅超过5%的股票',
        '找出换手率在3%-10%之间的活跃股票',
        '筛选振幅超过8%的高波动股票'
      ]
    },
    {
      category: '财务指标筛选',
      examples: [
        '找出净资产收益率超过15%的高盈利股票',
        '筛选净利润增长率超过20%的成长股',
        '找出资产负债率低于50%的稳健股票'
      ]
    },
    {
      category: '行业板块筛选',
      examples: [
        '找出银行行业的所有股票',
        '筛选科创板的股票',
        '找出创业板中市值超过100亿的科技股'
      ]
    },
    {
      category: '复合条件筛选',
      examples: [
        '找出银行行业中，市盈率低于10倍，净资产收益率超过12%的股票',
        '筛选市值超过1000亿的股票，按涨跌幅降序排列',
        '找出涨幅最大的前10只股票'
      ]
    }
  ];

  res.json({
    code: 200,
    message: '获取示例成功',
    data: examples
  });
});

/**
 * POST /api/filter/validate
 * 验证筛选描述（不执行实际查询）
 */
router.post('/validate', async (req, res) => {
  try {
    const { description } = req.body;

    if (!description || typeof description !== 'string') {
      return res.status(400).json({
        code: 400,
        message: '请提供有效的筛选描述',
        data: null
      });
    }

    // 这里可以添加描述验证逻辑
    // 例如检查是否包含股票相关关键词
    const stockKeywords = ['股票', '市盈率', '市净率', '市值', '涨幅', '跌幅', '行业', '板块', '财务', '净利润', '收益率'];
    const hasStockKeyword = stockKeywords.some(keyword => description.includes(keyword));

    if (!hasStockKeyword) {
      return res.json({
        code: 400,
        message: '描述似乎与股票筛选无关，请提供更具体的股票筛选条件',
        data: {
          valid: false,
          suggestions: [
            '请包含具体的筛选指标，如：市盈率、市值、涨跌幅等',
            '可以指定行业或板块，如：银行、科创板等',
            '可以设置数值范围，如：10-20倍、超过100亿等'
          ]
        }
      });
    }

    res.json({
      code: 200,
      message: '描述验证通过',
      data: {
        valid: true,
        description: description,
        estimatedComplexity: description.split('，').length > 2 ? 'high' : 'medium'
      }
    });

  } catch (error) {
    console.error('验证API错误:', error);
    res.status(500).json({
      code: 500,
      message: '验证失败',
      data: null
    });
  }
});

// 导出路由器
module.exports = router;

/**
 * 在主应用中使用示例：
 * 
 * const express = require('express');
 * const filterRoutes = require('./src/services/filter/apiExample');
 * 
 * const app = express();
 * app.use(express.json());
 * app.use('/api/filter', filterRoutes);
 * 
 * app.listen(3000, () => {
 *   console.log('服务器运行在端口 3000');
 * });
 */

/**
 * 前端调用示例：
 * 
 * // JavaScript/TypeScript
 * const response = await fetch('/api/filter/intelligent', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json'
 *   },
 *   body: JSON.stringify({
 *     description: '找出市盈率在10-30倍之间的银行股',
 *     targetDate: '2024-12-20'
 *   })
 * });
 * 
 * const result = await response.json();
 * console.log(result.data.stocks);
 * 
 * // 微信小程序
 * wx.request({
 *   url: 'https://your-domain.com/api/filter/intelligent',
 *   method: 'POST',
 *   data: {
 *     description: '找出市盈率在10-30倍之间的银行股',
 *     targetDate: '2024-12-20'
 *   },
 *   success: (res) => {
 *     console.log(res.data.data.stocks);
 *   }
 * });
 */
