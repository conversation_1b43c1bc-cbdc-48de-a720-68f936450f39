const { generateTextWithDeepSeek } = require('../../utils/deepseekApi');
const DailyStockData = require('../../models/DailyStockData');
const FinancialData = require('../../models/FinancialData');
const StockBasicInfo = require('../../models/StockBasicInfo');

/**
 * 智能股票筛选服务
 * 根据用户的自然语言描述和目标日期，使用AI解析生成查询脚本并执行筛选
 * @param {string} userDescription 用户的自然语言描述
 * @param {string} targetDate 目标日期 (YYYY-MM-DD格式)
 * @returns {Promise<Object>} 筛选结果
 */
const intelligentStockFilter = async (userDescription, targetDate) => {
  try {
    console.log('开始智能股票筛选...');
    console.log('用户描述:', userDescription);
    console.log('目标日期:', targetDate);

    // 第一步：使用AI解析用户描述，生成MongoDB查询脚本
    const queryScript = await generateQueryScript(userDescription, targetDate);
    console.log('生成的查询脚本:', queryScript);

    // 第二步：执行查询脚本获取符合条件的股票
    const filteredStocks = await executeQueryScript(queryScript, targetDate);
    console.log('筛选到的股票数量:', filteredStocks.length);

    // 第三步：返回结果
    return {
      success: true,
      userDescription,
      targetDate,
      queryScript,
      totalCount: filteredStocks.length,
      stocks: filteredStocks,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('智能股票筛选失败:', error);
    throw new Error(`智能股票筛选失败: ${error.message}`);
  }
};

/**
 * 使用AI生成MongoDB查询脚本
 * @param {string} userDescription 用户描述
 * @param {string} targetDate 目标日期
 * @returns {Promise<Object>} 查询脚本对象
 */
const generateQueryScript = async (userDescription, targetDate) => {
  try {
    // 构建系统提示词
    const systemPrompt = buildSystemPrompt();
    
    // 构建用户提示词
    const userPrompt = buildUserPrompt(userDescription, targetDate);
    
    // 调用DeepSeek API
    const response = await generateTextWithDeepSeek(userPrompt, systemPrompt, {
      temperature: 0.1,
      maxTokens: 3000
    });
    
    // 解析AI返回的查询脚本
    const queryScript = parseQueryScript(response);
    
    return queryScript;
  } catch (error) {
    throw new Error(`生成查询脚本失败: ${error.message}`);
  }
};

/**
 * 构建系统提示词
 * @returns {string} 系统提示词
 */
const buildSystemPrompt = () => {
  // 获取数据模型字段信息
  const dailyFields = getDailyStockDataFields();
  const financialFields = getFinancialDataFields();
  const basicInfoFields = getStockBasicInfoFields();

  return `你是一个专业的股票数据查询专家。用户会用自然语言描述股票筛选需求，你需要将其转换为MongoDB查询脚本。

**可用的数据表和字段：**

**1. DailyStockData (当日股票数据表)：**
${dailyFields}

**2. FinancialData (财务数据表)：**
${financialFields}

**3. StockBasicInfo (股票基本信息表)：**
${basicInfoFields}

**输出格式要求：**
你必须返回一个JSON对象，包含以下结构：
{
  "queryType": "single|aggregate|multi",
  "description": "查询描述",
  "queries": [
    {
      "collection": "DailyStockData|FinancialData|StockBasicInfo",
      "operation": "find|aggregate",
      "pipeline": [...] // 如果是aggregate操作
      "filter": {...}, // 如果是find操作
      "projection": {...}, // 可选，指定返回字段
      "sort": {...}, // 可选，排序
      "limit": 数字 // 可选，限制结果数量
    }
  ],
  "joinLogic": "intersection|union", // 多表查询时的连接逻辑
  "resultFields": ["字段1", "字段2", ...] // 最终返回的字段列表
}

**重要规则：**
1. 只使用上述列出的字段名，不要创造新的字段
2. 数值比较使用MongoDB操作符：$gt, $gte, $lt, $lte, $eq, $ne
3. 范围查询使用：{$gte: 最小值, $lte: 最大值}
4. 多选条件使用：{$in: [值1, 值2, ...]}
5. 文本搜索使用：{$regex: "关键词", $options: "i"}
6. 如果需要多表关联，使用aggregate的$lookup操作
7. 确保返回的是有效的JSON格式
8. 如果用户描述不清楚或与股票无关，返回空的查询条件

**示例：**
用户："找出市盈率在10-20倍之间，市值超过100亿的股票"
返回：
{
  "queryType": "single",
  "description": "筛选市盈率10-20倍且市值超过100亿的股票",
  "queries": [
    {
      "collection": "DailyStockData",
      "operation": "find",
      "filter": {
        "peRatioTTM": {"$gte": 10, "$lte": 20},
        "totalMarketCap": {"$gt": 100}
      },
      "projection": {"stockCode": 1, "peRatioTTM": 1, "totalMarketCap": 1, "_id": 0}
    }
  ],
  "resultFields": ["stockCode", "peRatioTTM", "totalMarketCap"]
}`;
};

/**
 * 获取当日股票数据字段信息
 * @returns {string} 字段信息字符串
 */
const getDailyStockDataFields = () => {
  return `- stockCode: 证券代码 (String)
- timestamp: 时间戳 (Date)
- closePrice: 收盘价 (Number)
- priceChange: 涨跌额 (Number)
- changePercent: 涨跌幅% (Number)
- previousClose: 昨收 (Number)
- openPrice: 今开 (Number)
- highPrice: 最高 (Number)
- lowPrice: 最低 (Number)
- volume: 成交量 (Number)
- turnover: 成交额 (Number)
- turnoverRate: 换手率% (Number)
- amplitude: 振幅% (Number)
- limitUpPrice: 涨停价 (Number)
- limitDownPrice: 跌停价 (Number)
- averagePrice: 均价 (Number)
- week52High: 52周最高 (Number)
- week52Low: 52周最低 (Number)
- ytdGain: 今年以来涨幅% (Number)
- peRatioDynamic: 市盈率(动) (Number)
- peRatioStatic: 市盈率(静) (Number)
- peRatioTTM: 市盈率(TTM) (Number)
- pbRatio: 市净率 (Number)
- earningsPerShare: 每股收益 (Number)
- bookValuePerShare: 每股净资产 (Number)
- dividendTTM: 股息(TTM) (Number)
- dividendYieldTTM: 股息率(TTM)% (Number)
- totalMarketCap: 总市值(亿元) (Number)
- circulatingMarketCap: 流通市值(亿元) (Number)
- volumeRatio: 量比 (Number)
- priceVelocity: 涨速% (Number)
- fiveMinuteChange: 5分钟涨跌% (Number)
- sixtyDayChange: 60日涨跌幅% (Number)`;
};

/**
 * 获取财务数据字段信息
 * @returns {string} 字段信息字符串
 */
const getFinancialDataFields = () => {
  return `- stockCode: 证券代码 (String)
- reportType: 报表类型 (String) [按年度, 按报告期, 按单季度]
- fiscalYear: 财政年度 (Number)
- reportDate: 报告日期 (Date)
- data.keyMetrics.netProfit: 净利润(亿元) (Number)
- data.keyMetrics.netProfitGrowthRate: 净利润同比增长率% (Number)
- data.keyMetrics.nonRecurringNetProfit: 扣非净利润(亿元) (Number)
- data.keyMetrics.nonRecurringNetProfitGrowthRate: 扣非净利润同比增长率% (Number)
- data.keyMetrics.totalRevenue: 营业总收入(亿元) (Number)
- data.keyMetrics.totalRevenueGrowthRate: 营业总收入同比增长率% (Number)
- data.keyMetrics.netProfitMargin: 销售净利率% (Number)
- data.keyMetrics.grossProfitMargin: 销售毛利率% (Number)
- data.keyMetrics.basicEarningsPerShare: 基本每股收益 (Number)
- data.keyMetrics.bookValuePerShare: 每股净资产 (Number)
- data.keyMetrics.returnOnEquity: 净资产收益率% (Number)
- data.keyMetrics.dilutedReturnOnEquity: 净资产收益率-摊薄% (Number)
- data.keyMetrics.currentRatio: 流动比率 (Number)
- data.keyMetrics.quickRatio: 速动比率 (Number)
- data.keyMetrics.equityRatio: 产权比率 (Number)
- data.keyMetrics.debtToAssetRatio: 资产负债率% (Number)
- data.keyMetrics.inventoryTurnoverRatio: 存货周转率 (Number)
- data.keyMetrics.inventoryTurnoverDays: 存货周转天数 (Number)
- data.keyMetrics.accountsReceivableTurnoverDays: 应收账款周转天数 (Number)`;
};

/**
 * 获取股票基本信息字段信息
 * @returns {string} 字段信息字符串
 */
const getStockBasicInfoFields = () => {
  return `- stockCode: 证券代码 (String)
- stockName: 证券简称 (String)
- board: 板块 (String) [沪市主板, 深市主板, 创业板, 科创板]
- industry: 行业 (String)
- listingDate: 上市时间 (Date)`;
};

/**
 * 构建用户提示词
 * @param {string} userDescription 用户描述
 * @param {string} targetDate 目标日期
 * @returns {string} 用户提示词
 */
const buildUserPrompt = (userDescription, targetDate) => {
  return `请将以下自然语言描述转换为MongoDB查询脚本：

用户描述："${userDescription}"
目标日期：${targetDate}

请仔细分析用户需求，识别涉及的数据表、筛选条件和排序要求，然后返回对应的MongoDB查询脚本JSON格式。

注意：
1. 如果涉及日期筛选，请考虑目标日期
2. 如果用户提到"最新"、"当前"等词汇，优先使用当日数据
3. 如果用户提到财务指标，使用最新年度的财务数据
4. 合理设置返回字段，包含用户关心的指标
5. 只返回JSON，不要任何额外说明`;
};

/**
 * 解析AI返回的查询脚本
 * @param {string} response AI返回的响应
 * @returns {Object} 解析后的查询脚本对象
 */
const parseQueryScript = (response) => {
  try {
    // 去除可能的Markdown代码块标记
    let cleanResponse = response.trim();
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.replace(/^```json\s*/, '');
    }
    if (cleanResponse.startsWith('```')) {
      cleanResponse = cleanResponse.replace(/^```\s*/, '');
    }
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.replace(/\s*```$/, '');
    }

    // 解析JSON
    const parsed = JSON.parse(cleanResponse);

    // 验证必要字段
    if (!parsed.queryType || !parsed.queries || !Array.isArray(parsed.queries)) {
      throw new Error('查询脚本格式不正确：缺少必要字段');
    }

    return parsed;
  } catch (error) {
    throw new Error(`无法解析AI返回的查询脚本: ${error.message}。返回内容: ${response}`);
  }
};

/**
 * 执行查询脚本
 * @param {Object} queryScript 查询脚本对象
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 查询结果
 */
const executeQueryScript = async (queryScript, targetDate) => {
  try {
    const { queryType, queries, joinLogic = 'intersection', resultFields = [] } = queryScript;

    let allResults = [];

    // 执行每个查询
    for (const query of queries) {
      const result = await executeQuery(query, targetDate);
      allResults.push({
        collection: query.collection,
        data: result
      });
    }

    // 根据查询类型处理结果
    if (queryType === 'single' && allResults.length === 1) {
      return formatResults(allResults[0].data, resultFields);
    } else if (queryType === 'multi') {
      return handleMultiTableResults(allResults, joinLogic, resultFields);
    } else {
      // 默认返回第一个查询的结果
      return formatResults(allResults[0]?.data || [], resultFields);
    }
  } catch (error) {
    throw new Error(`执行查询脚本失败: ${error.message}`);
  }
};

/**
 * 执行单个查询
 * @param {Object} query 查询对象
 * @param {string} targetDate 目标日期
 * @returns {Promise<Array>} 查询结果
 */
const executeQuery = async (query, targetDate) => {
  try {
    const { collection, operation, filter = {}, pipeline = [], projection = {}, sort = {}, limit } = query;

    // 获取对应的模型
    const Model = getModel(collection);

    // 处理日期相关的筛选条件
    const processedFilter = processDateFilter(filter, targetDate, collection);

    let result;

    if (operation === 'aggregate') {
      // 执行聚合查询
      result = await Model.aggregate(pipeline);
    } else {
      // 执行普通查询
      let queryBuilder = Model.find(processedFilter);

      if (Object.keys(projection).length > 0) {
        queryBuilder = queryBuilder.select(projection);
      }

      if (Object.keys(sort).length > 0) {
        queryBuilder = queryBuilder.sort(sort);
      }

      if (limit && typeof limit === 'number') {
        queryBuilder = queryBuilder.limit(limit);
      }

      result = await queryBuilder.exec();
    }

    return result;
  } catch (error) {
    throw new Error(`执行查询失败: ${error.message}`);
  }
};

/**
 * 获取对应的数据模型
 * @param {string} collection 集合名称
 * @returns {Object} Mongoose模型
 */
const getModel = (collection) => {
  switch (collection) {
    case 'DailyStockData':
      return DailyStockData;
    case 'FinancialData':
      return FinancialData;
    case 'StockBasicInfo':
      return StockBasicInfo;
    default:
      throw new Error(`未知的集合名称: ${collection}`);
  }
};

/**
 * 处理日期相关的筛选条件
 * @param {Object} filter 原始筛选条件
 * @param {string} targetDate 目标日期
 * @param {string} collection 集合名称
 * @returns {Object} 处理后的筛选条件
 */
const processDateFilter = (filter, targetDate, collection) => {
  const processedFilter = { ...filter };

  // 如果是财务数据，添加最新年度筛选
  if (collection === 'FinancialData') {
    if (!processedFilter.reportType) {
      processedFilter.reportType = '按年度';
    }
    if (!processedFilter.fiscalYear && targetDate) {
      const year = new Date(targetDate).getFullYear();
      // 使用目标日期的前一年作为财务数据年度（因为财务数据通常有延迟）
      processedFilter.fiscalYear = year - 1;
    }
  }

  // 如果是当日数据且指定了目标日期，可以添加日期筛选
  // 注意：根据当前数据模型，DailyStockData使用stockCode作为唯一索引
  // 所以这里暂时不添加日期筛选，除非数据模型支持多日期数据

  return processedFilter;
};

/**
 * 处理多表查询结果
 * @param {Array} allResults 所有查询结果
 * @param {string} joinLogic 连接逻辑
 * @param {Array} resultFields 结果字段
 * @returns {Array} 处理后的结果
 */
const handleMultiTableResults = async (allResults, joinLogic, resultFields) => {
  try {
    if (allResults.length === 0) return [];

    // 提取所有股票代码
    const stockCodeSets = allResults.map(result => {
      const stockCodes = result.data.map(item => item.stockCode).filter(Boolean);
      return new Set(stockCodes);
    });

    let finalStockCodes;

    if (joinLogic === 'intersection') {
      // 取交集
      finalStockCodes = stockCodeSets.reduce((intersection, currentSet) => {
        return new Set([...intersection].filter(code => currentSet.has(code)));
      });
    } else {
      // 取并集
      finalStockCodes = stockCodeSets.reduce((union, currentSet) => {
        return new Set([...union, ...currentSet]);
      }, new Set());
    }

    // 根据最终的股票代码获取完整信息
    const finalStockCodesArray = Array.from(finalStockCodes);
    const detailedResults = await getDetailedStockInfo(finalStockCodesArray, resultFields);

    return detailedResults;
  } catch (error) {
    throw new Error(`处理多表查询结果失败: ${error.message}`);
  }
};

/**
 * 获取股票详细信息
 * @param {Array} stockCodes 股票代码列表
 * @param {Array} resultFields 需要返回的字段
 * @returns {Promise<Array>} 股票详细信息
 */
const getDetailedStockInfo = async (stockCodes, resultFields) => {
  try {
    if (stockCodes.length === 0) return [];

    // 获取基本信息
    const basicInfoPromise = StockBasicInfo.find(
      { stockCode: { $in: stockCodes } },
      { _id: 0 }
    );

    // 获取当日数据
    const dailyDataPromise = DailyStockData.find(
      { stockCode: { $in: stockCodes } },
      { _id: 0 }
    );

    // 获取最新财务数据
    const financialDataPromise = getLatestFinancialData(stockCodes);

    const [basicInfo, dailyData, financialData] = await Promise.all([
      basicInfoPromise,
      dailyDataPromise,
      financialDataPromise
    ]);

    // 创建数据映射
    const basicInfoMap = createDataMap(basicInfo, 'stockCode');
    const dailyDataMap = createDataMap(dailyData, 'stockCode');
    const financialDataMap = createDataMap(financialData, 'stockCode');

    // 合并数据
    const results = stockCodes.map(stockCode => {
      const result = {
        证券代码: stockCode,
        证券简称: basicInfoMap[stockCode]?.stockName || '',
      };

      // 添加基本信息
      if (basicInfoMap[stockCode]) {
        const basic = basicInfoMap[stockCode];
        if (basic.board) result.板块 = basic.board;
        if (basic.industry) result.行业 = basic.industry;
        if (basic.listingDate) result.上市时间 = basic.listingDate;
      }

      // 添加当日数据
      if (dailyDataMap[stockCode]) {
        const daily = dailyDataMap[stockCode];
        if (daily.closePrice !== null) result.收盘价 = daily.closePrice;
        if (daily.changePercent !== null) result.涨跌幅 = `${daily.changePercent}%`;
        if (daily.peRatioTTM !== null) result.市盈率TTM = daily.peRatioTTM;
        if (daily.pbRatio !== null) result.市净率 = daily.pbRatio;
        if (daily.totalMarketCap !== null) result.总市值 = `${daily.totalMarketCap}亿`;
        if (daily.turnoverRate !== null) result.换手率 = `${daily.turnoverRate}%`;
      }

      // 添加财务数据
      if (financialDataMap[stockCode]) {
        const financial = financialDataMap[stockCode];
        const metrics = financial.data?.keyMetrics;
        if (metrics) {
          if (metrics.netProfitGrowthRate !== null) result.净利润增长率 = `${metrics.netProfitGrowthRate}%`;
          if (metrics.returnOnEquity !== null) result.净资产收益率 = `${metrics.returnOnEquity}%`;
          if (metrics.netProfitMargin !== null) result.销售净利率 = `${metrics.netProfitMargin}%`;
          if (metrics.debtToAssetRatio !== null) result.资产负债率 = `${metrics.debtToAssetRatio}%`;
        }
      }

      return result;
    });

    return results;
  } catch (error) {
    throw new Error(`获取股票详细信息失败: ${error.message}`);
  }
};

/**
 * 获取最新财务数据
 * @param {Array} stockCodes 股票代码列表
 * @returns {Promise<Array>} 最新财务数据
 */
const getLatestFinancialData = async (stockCodes) => {
  try {
    // 获取最新年度
    const latestYearResult = await FinancialData.findOne(
      { reportType: '按年度' },
      { fiscalYear: 1, _id: 0 }
    ).sort({ fiscalYear: -1 });

    const latestYear = latestYearResult ? latestYearResult.fiscalYear : new Date().getFullYear() - 1;

    return await FinancialData.find({
      stockCode: { $in: stockCodes },
      reportType: '按年度',
      fiscalYear: latestYear
    }, { _id: 0 });
  } catch (error) {
    console.error('获取最新财务数据失败:', error);
    return [];
  }
};

/**
 * 创建数据映射
 * @param {Array} dataArray 数据数组
 * @param {string} keyField 键字段
 * @returns {Object} 数据映射对象
 */
const createDataMap = (dataArray, keyField) => {
  return dataArray.reduce((map, item) => {
    if (item[keyField]) {
      map[item[keyField]] = item;
    }
    return map;
  }, {});
};

/**
 * 格式化查询结果
 * @param {Array} data 原始数据
 * @param {Array} resultFields 结果字段
 * @returns {Array} 格式化后的结果
 */
const formatResults = (data, resultFields) => {
  if (!Array.isArray(data) || data.length === 0) return [];

  // 如果指定了结果字段，只返回这些字段
  if (resultFields && resultFields.length > 0) {
    return data.map(item => {
      const result = {};
      resultFields.forEach(field => {
        if (item[field] !== undefined) {
          // 转换为中文字段名
          const chineseField = getChineseFieldName(field);
          result[chineseField] = formatFieldValue(field, item[field]);
        }
      });
      return result;
    });
  }

  // 否则返回所有数据，转换为中文字段名
  return data.map(item => {
    const result = {};
    Object.keys(item.toObject ? item.toObject() : item).forEach(field => {
      if (field !== '_id' && field !== '__v') {
        const chineseField = getChineseFieldName(field);
        result[chineseField] = formatFieldValue(field, item[field]);
      }
    });
    return result;
  });
};

/**
 * 获取字段的中文名称
 * @param {string} englishField 英文字段名
 * @returns {string} 中文字段名
 */
const getChineseFieldName = (englishField) => {
  // 尝试从各个模型获取中文字段名
  try {
    let chineseName = DailyStockData.getChineseFieldName(englishField);
    if (chineseName !== englishField) return chineseName;

    chineseName = FinancialData.getChineseFieldName(englishField);
    if (chineseName !== englishField) return chineseName;

    chineseName = StockBasicInfo.getChineseFieldName(englishField);
    if (chineseName !== englishField) return chineseName;
  } catch (error) {
    console.warn('获取中文字段名失败:', error.message);
  }

  return englishField;
};

/**
 * 格式化字段值
 * @param {string} field 字段名
 * @param {*} value 字段值
 * @returns {*} 格式化后的值
 */
const formatFieldValue = (field, value) => {
  if (value === null || value === undefined) return null;

  // 百分比字段
  if (field.includes('Percent') || field.includes('Rate') || field.includes('Ratio') ||
      field.includes('Margin') || field.includes('Yield')) {
    return `${value}%`;
  }

  // 市值字段
  if (field.includes('MarketCap') || field.includes('Revenue') || field.includes('Profit')) {
    return `${value}亿`;
  }

  // 日期字段
  if (field.includes('Date') || field.includes('timestamp')) {
    return new Date(value).toLocaleDateString('zh-CN');
  }

  return value;
};

module.exports = {
  intelligentStockFilter
};
