// app.js
const { formatDate } = require('./utils/common');
const storage = require('./utils/storage');
const { reportError } = require('./utils/error');
const migration = require('./utils/migration');

App({
  /**
   * 全局数据
   */
  globalData: {
    // API配置
    // baseUrl: 'http://localhost:3000/api/v1',
    baseUrl: 'https://x-xuan.com/api/v1',
    
    // 用户信息
    userInfo: null,
    isLoggedIn: false,
    
    // 应用信息
    appName: '言策AI',
    version: '1.0.0',
    
    // 系统信息
    systemInfo: null,
    
    // 状态管理
    pageStack: []
  },

  /**
   * 小程序初始化完成时触发，全局只触发一次
   */
  onLaunch(options) {
    console.log('言策AI小程序启动', options);
    
    // 获取系统信息
    this.getSystemInfo();
    
    // 检查更新
    this.checkForUpdate();
    
    // 数据迁移（清理旧版本重复字段）
    this.performDataMigration();
    
    // 初始化用户状态
    this.initUserStatus();
    
    // 设置全局请求拦截器
    this.setupRequestInterceptor();
  },

  /**
   * 小程序启动，或从后台进入前台显示时触发
   */
  onShow(options) {
    console.log('言策AI小程序显示', options);
    
    // 更新页面栈
    this.updatePageStack();
    
    // 检查登录状态
    this.checkLoginStatus();
  },

  /**
   * 小程序从前台进入后台时触发
   */
  onHide() {
    console.log('言策AI小程序隐藏');
  },

  /**
   * 小程序发生脚本错误或 API 调用报错时触发
   */
  onError(msg) {
    console.error('言策AI小程序错误:', msg);
    
    // 错误上报
    this.reportError(msg);
  },

  /**
   * 小程序要打开的页面不存在时触发
   */
  onPageNotFound(res) {
    console.error('页面不存在:', res);
    
    // 跳转到首页
    wx.switchTab({
      url: '/pages/user/userIndex/userIndex'
    });
  },

  /**
   * 获取系统信息
   */
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      this.globalData.systemInfo = systemInfo;
      
      console.log('系统信息:', systemInfo);
      
      // 设置状态栏高度等全局样式变量
      this.setGlobalStyles(systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
    }
  },

  /**
   * 设置全局样式变量
   */
  setGlobalStyles(systemInfo) {
    // 计算状态栏高度、导航栏高度等
    const statusBarHeight = systemInfo.statusBarHeight || 20;
    const navBarHeight = 44; // 小程序导航栏标准高度
    const totalNavHeight = statusBarHeight + navBarHeight;
    
    // 将样式变量存储到全局数据中，供各页面使用
    this.globalData.styleVars = {
      statusBarHeight: statusBarHeight + 'px',
      navBarHeight: navBarHeight + 'px', 
      totalNavHeight: totalNavHeight + 'px',
      screenWidth: systemInfo.screenWidth + 'px',
      screenHeight: systemInfo.screenHeight + 'px',
      windowWidth: systemInfo.windowWidth + 'px',
      windowHeight: systemInfo.windowHeight + 'px'
    };
  },

  /**
   * 检查小程序更新
   */
  checkForUpdate() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果:', res.hasUpdate);
      });
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });
      
      updateManager.onUpdateFailed(() => {
        console.error('新版本更新失败');
      });
    }
  },

  /**
   * 执行数据迁移
   */
  performDataMigration() {
    try {
      if (migration.needMigration()) {
        console.log('检测到旧版本数据，开始迁移...');
        migration.migrateUserData();
      }
      
      // 输出存储统计信息
      migration.getStorageStats();
    } catch (error) {
      console.error('数据迁移失败:', error);
    }
  },

  /**
   * 初始化用户状态
   */
  async initUserStatus() {
    try {
      // 使用统一的存储工具获取用户信息
      const userInfo = storage.getUserInfo();
      const token = storage.getToken();
      
      if (userInfo && token) {
        this.globalData.userInfo = userInfo;
        this.globalData.isLoggedIn = true;
      }
      
      console.log('用户状态初始化完成:', {
        isLoggedIn: this.globalData.isLoggedIn,
        hasUserInfo: !!this.globalData.userInfo
      });
    } catch (error) {
      console.error('初始化用户状态失败:', error);
    }
  },

  /**
   * 更新用户登录状态
   */
  updateUserLoginStatus(userInfo, token) {
    this.globalData.userInfo = userInfo;
    this.globalData.isLoggedIn = true;
    
    // 使用统一的存储工具保存数据
    if (userInfo) {
      storage.setUserInfo(userInfo);
    }
    if (token) {
      storage.setToken(token);
    }
  },

  /**
   * 清除用户登录状态
   */
  clearUserLoginStatus() {
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    
    // 使用统一的存储工具清除数据
    storage.clearUserData();
  },

  /**
   * 检查登录状态（仅验证token有效性）
   * @param {Object} options 选项配置
   * @param {boolean} options.showToast 是否显示提示信息
   * @param {boolean} options.redirectToLogin 是否跳转到登录页
   * @param {boolean} options.updateUserInfo 是否同时更新用户信息
   * @param {Function} options.onSuccess 成功回调
   * @param {Function} options.onFail 失败回调
   * @returns {Promise<boolean>} 是否已登录
   */
  async checkLoginStatus(options = {}) {
    const {
      showToast = false,
      redirectToLogin = false,
      updateUserInfo = true,
      onSuccess,
      onFail
    } = options;

    const token = storage.getToken();
    
    if (!token) {
      this.globalData.isLoggedIn = false;
      this.globalData.userInfo = null;
      
      if (onFail) onFail({ code: 'NO_TOKEN', message: '未找到登录令牌' });
      
      if (redirectToLogin) {
        this.redirectToLogin();
      }
      
      return false;
    }
    
    try {
      // 如果需要更新用户信息，则调用后端验证并获取最新用户信息
      if (updateUserInfo) {
        const userInfo = await this.getCurrentUserInfo();
        if (userInfo) {
          if (onSuccess) onSuccess(userInfo);
          return true;
        } else {
          throw new Error('用户信息验证失败');
        }
      } else {
        // 仅检查token存在性和全局状态
        if (this.globalData.isLoggedIn && this.globalData.userInfo) {
          if (onSuccess) onSuccess(this.globalData.userInfo);
          return true;
        } else {
          // 如果全局状态不完整，建议调用完整验证
          return await this.checkLoginStatus({ ...options, updateUserInfo: true });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
      
      // 清除无效的登录信息
      this.clearUserLoginStatus();
      
      if (showToast && !error.message.includes('需要登录')) {
        wx.showToast({
          title: '登录状态异常',
          icon: 'none',
          duration: 2000
        });
      }
      
      if (onFail) onFail(error);
      
      if (redirectToLogin) {
        this.redirectToLogin();
      }
      
      return false;
    }
  },

  /**
   * 获取当前用户信息（独立的用户信息更新方法）
   * @returns {Promise<Object|null>} 用户信息对象，失败时返回null
   */
  async getCurrentUserInfo() {
    try {
      // 添加调试信息
      const token = storage.getToken();
      console.log('getCurrentUserInfo - 当前token:', token ? '有token' : '无token');
      
      const auth = require('./api/auth');
      const userInfo = await auth.getCurrentUser();
      
      if (userInfo && userInfo.success) {
        // 更新全局状态
        this.globalData.isLoggedIn = true;
        this.globalData.userInfo = userInfo.data;
        
        // 同步到本地存储
        storage.setUserInfo(userInfo.data);
        
        console.log('getCurrentUserInfo - 用户信息获取成功:', userInfo.data.nickname || '未知用户');
        return userInfo.data;
      } else {
        console.log('getCurrentUserInfo - 后端返回失败:', userInfo);
        return null;
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  },

  /**
   * 刷新用户信息（供外部调用）
   */
  async refreshUserInfo() {
    return await this.getCurrentUserInfo();
  },

  /**
   * 跳转到登录页面
   */
  redirectToLogin() {
    // 获取当前页面
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';
    
    // 如果已经在登录页面，则不跳转
    if (currentRoute.includes('login')) {
      return;
    }
    
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 设置全局请求拦截器
   */
  setupRequestInterceptor() {
    // 原始request方法
    const originalRequest = wx.request;
    
    // 重写request方法
    wx.request = (options) => {
      // 添加baseUrl
      if (options.url && !options.url.startsWith('http')) {
        options.url = this.globalData.baseUrl + options.url;
      }
      
      // 添加通用header
      options.header = {
        'Content-Type': 'application/json',
        ...options.header
      };
      
      // 添加token
      const token = storage.getToken();
      if (token) {
        options.header.Authorization = `Bearer ${token}`;
      }
      
      // 请求开始
      console.log('请求开始:', options.url);
      
      // 保存原始回调
      const originalSuccess = options.success;
      const originalFail = options.fail;
      
      // 重写success回调
      options.success = (res) => {
        console.log('请求成功:', options.url, res);
        
        // 处理token过期
        if (res.data && res.data.code === 401) {
          this.handleTokenExpired();
        }
        
        if (originalSuccess) {
          originalSuccess(res);
        }
      };
      
      // 重写fail回调
      options.fail = (err) => {
        console.error('请求失败:', options.url, err);
        
        if (originalFail) {
          originalFail(err);
        }
      };
      
      // 调用原始request
      return originalRequest(options);
    };
  },

  /**
   * 处理token过期
   */
  handleTokenExpired() {
    // 防止重复处理
    if (this.tokenExpiredHandling) {
      return;
    }
    
    this.tokenExpiredHandling = true;
    
    // 使用统一的清除方法
    this.clearUserLoginStatus();
    
    // 获取当前页面栈
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentRoute = currentPage ? currentPage.route : '';
    
    // 如果当前在登录页面，不显示提示
    if (currentRoute.includes('login')) {
      this.tokenExpiredHandling = false;
      return;
    }
    
    // 跳转到登录页
    wx.showModal({
      title: '登录过期',
      content: '您的登录已过期，请重新登录',
      showCancel: false,
      confirmText: '重新登录',
      success: () => {
        wx.navigateTo({
          url: '/pages/login/login',
          complete: () => {
            this.tokenExpiredHandling = false;
          }
        });
      },
      fail: () => {
        this.tokenExpiredHandling = false;
      }
    });
  },

  /**
   * 更新页面栈
   */
  updatePageStack() {
    const pages = getCurrentPages();
    this.globalData.pageStack = pages.map(page => page.route);
  },

  /**
   * 错误上报
   */
  reportError(msg) {
    // 使用错误处理工具进行上报
    reportError(msg, {
      source: 'app.js',
      globalData: this.globalData
    });
  }
});
