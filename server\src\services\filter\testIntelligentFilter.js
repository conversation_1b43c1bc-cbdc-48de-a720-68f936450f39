const { intelligentStockFilter } = require('./intelligentStockFilterService');
require('dotenv').config();

/**
 * 测试智能股票筛选服务
 */
const testIntelligentFilter = async () => {
  try {
    console.log('开始测试智能股票筛选服务...\n');

    // 测试用例1：基本的市盈率和市值筛选
    console.log('=== 测试用例1：市盈率和市值筛选 ===');
    const result1 = await intelligentStockFilter(
      '找出市盈率在10-30倍之间，总市值超过100亿的股票',
      '2024-12-20'
    );
    console.log('结果1:', JSON.stringify(result1, null, 2));
    console.log('\n');

    // 测试用例2：涨跌幅筛选
    console.log('=== 测试用例2：涨跌幅筛选 ===');
    const result2 = await intelligentStockFilter(
      '筛选今日涨幅在5%以上的股票',
      '2024-12-20'
    );
    console.log('结果2:', JSON.stringify(result2, null, 2));
    console.log('\n');

    // 测试用例3：行业筛选
    console.log('=== 测试用例3：行业筛选 ===');
    const result3 = await intelligentStockFilter(
      '找出银行行业的股票，按市值排序',
      '2024-12-20'
    );
    console.log('结果3:', JSON.stringify(result3, null, 2));
    console.log('\n');

    // 测试用例4：财务指标筛选
    console.log('=== 测试用例4：财务指标筛选 ===');
    const result4 = await intelligentStockFilter(
      '筛选净资产收益率超过15%，净利润增长率为正的股票',
      '2024-12-20'
    );
    console.log('结果4:', JSON.stringify(result4, null, 2));

  } catch (error) {
    console.error('测试失败:', error.message);
  }
};

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  testIntelligentFilter();
}

module.exports = { testIntelligentFilter };
